// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		255485C12E6589DC00AC0119 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 255485AB2E6589DB00AC0119 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 255485B22E6589DB00AC0119;
			remoteInfo = demo3;
		};
		255485CB2E6589DC00AC0119 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 255485AB2E6589DB00AC0119 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 255485B22E6589DB00AC0119;
			remoteInfo = demo3;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		255485B32E6589DB00AC0119 /* demo3.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = demo3.app; sourceTree = BUILT_PRODUCTS_DIR; };
		255485C02E6589DC00AC0119 /* demo3Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = demo3Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		255485CA2E6589DC00AC0119 /* demo3UITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = demo3UITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		255485B52E6589DB00AC0119 /* demo3 */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demo3;
			sourceTree = "<group>";
		};
		255485C32E6589DC00AC0119 /* demo3Tests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demo3Tests;
			sourceTree = "<group>";
		};
		255485CD2E6589DC00AC0119 /* demo3UITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = demo3UITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		255485B02E6589DB00AC0119 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		255485BD2E6589DC00AC0119 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		255485C72E6589DC00AC0119 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		255485AA2E6589DB00AC0119 = {
			isa = PBXGroup;
			children = (
				255485B52E6589DB00AC0119 /* demo3 */,
				255485C32E6589DC00AC0119 /* demo3Tests */,
				255485CD2E6589DC00AC0119 /* demo3UITests */,
				255485B42E6589DB00AC0119 /* Products */,
			);
			sourceTree = "<group>";
		};
		255485B42E6589DB00AC0119 /* Products */ = {
			isa = PBXGroup;
			children = (
				255485B32E6589DB00AC0119 /* demo3.app */,
				255485C02E6589DC00AC0119 /* demo3Tests.xctest */,
				255485CA2E6589DC00AC0119 /* demo3UITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		255485B22E6589DB00AC0119 /* demo3 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 255485D42E6589DC00AC0119 /* Build configuration list for PBXNativeTarget "demo3" */;
			buildPhases = (
				255485AF2E6589DB00AC0119 /* Sources */,
				255485B02E6589DB00AC0119 /* Frameworks */,
				255485B12E6589DB00AC0119 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				255485B52E6589DB00AC0119 /* demo3 */,
			);
			name = demo3;
			packageProductDependencies = (
			);
			productName = demo3;
			productReference = 255485B32E6589DB00AC0119 /* demo3.app */;
			productType = "com.apple.product-type.application";
		};
		255485BF2E6589DC00AC0119 /* demo3Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 255485D72E6589DC00AC0119 /* Build configuration list for PBXNativeTarget "demo3Tests" */;
			buildPhases = (
				255485BC2E6589DC00AC0119 /* Sources */,
				255485BD2E6589DC00AC0119 /* Frameworks */,
				255485BE2E6589DC00AC0119 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				255485C22E6589DC00AC0119 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				255485C32E6589DC00AC0119 /* demo3Tests */,
			);
			name = demo3Tests;
			packageProductDependencies = (
			);
			productName = demo3Tests;
			productReference = 255485C02E6589DC00AC0119 /* demo3Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		255485C92E6589DC00AC0119 /* demo3UITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 255485DA2E6589DC00AC0119 /* Build configuration list for PBXNativeTarget "demo3UITests" */;
			buildPhases = (
				255485C62E6589DC00AC0119 /* Sources */,
				255485C72E6589DC00AC0119 /* Frameworks */,
				255485C82E6589DC00AC0119 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				255485CC2E6589DC00AC0119 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				255485CD2E6589DC00AC0119 /* demo3UITests */,
			);
			name = demo3UITests;
			packageProductDependencies = (
			);
			productName = demo3UITests;
			productReference = 255485CA2E6589DC00AC0119 /* demo3UITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		255485AB2E6589DB00AC0119 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					255485B22E6589DB00AC0119 = {
						CreatedOnToolsVersion = 16.4;
					};
					255485BF2E6589DC00AC0119 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 255485B22E6589DB00AC0119;
					};
					255485C92E6589DC00AC0119 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 255485B22E6589DB00AC0119;
					};
				};
			};
			buildConfigurationList = 255485AE2E6589DB00AC0119 /* Build configuration list for PBXProject "demo3" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 255485AA2E6589DB00AC0119;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 255485B42E6589DB00AC0119 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				255485B22E6589DB00AC0119 /* demo3 */,
				255485BF2E6589DC00AC0119 /* demo3Tests */,
				255485C92E6589DC00AC0119 /* demo3UITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		255485B12E6589DB00AC0119 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		255485BE2E6589DC00AC0119 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		255485C82E6589DC00AC0119 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		255485AF2E6589DB00AC0119 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		255485BC2E6589DC00AC0119 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		255485C62E6589DC00AC0119 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		255485C22E6589DC00AC0119 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 255485B22E6589DB00AC0119 /* demo3 */;
			targetProxy = 255485C12E6589DC00AC0119 /* PBXContainerItemProxy */;
		};
		255485CC2E6589DC00AC0119 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 255485B22E6589DB00AC0119 /* demo3 */;
			targetProxy = 255485CB2E6589DC00AC0119 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		255485D22E6589DC00AC0119 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		255485D32E6589DC00AC0119 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		255485D52E6589DC00AC0119 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demo3;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		255485D62E6589DC00AC0119 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demo3;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		255485D82E6589DC00AC0119 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demo3Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/demo3.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/demo3";
			};
			name = Debug;
		};
		255485D92E6589DC00AC0119 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demo3Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/demo3.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/demo3";
			};
			name = Release;
		};
		255485DB2E6589DC00AC0119 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demo3UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = demo3;
			};
			name = Debug;
		};
		255485DC2E6589DC00AC0119 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SWS8AMQ6P9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ltt.demo3UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = demo3;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		255485AE2E6589DB00AC0119 /* Build configuration list for PBXProject "demo3" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				255485D22E6589DC00AC0119 /* Debug */,
				255485D32E6589DC00AC0119 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		255485D42E6589DC00AC0119 /* Build configuration list for PBXNativeTarget "demo3" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				255485D52E6589DC00AC0119 /* Debug */,
				255485D62E6589DC00AC0119 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		255485D72E6589DC00AC0119 /* Build configuration list for PBXNativeTarget "demo3Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				255485D82E6589DC00AC0119 /* Debug */,
				255485D92E6589DC00AC0119 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		255485DA2E6589DC00AC0119 /* Build configuration list for PBXNativeTarget "demo3UITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				255485DB2E6589DC00AC0119 /* Debug */,
				255485DC2E6589DC00AC0119 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 255485AB2E6589DB00AC0119 /* Project object */;
}

//
//  AlbumDetailView.swift
//  demo3
//
//  Created by ltt on 2025/9/1.
//

import SwiftUI

struct AlbumDetailView: View {
    @State private var isPlaying = false
    @State private var selectedBottomTab = 0
    @Environment(\.presentationMode) var presentationMode
    
    let songs = [
        <PERSON>(title: "brutal", artist: "<PERSON> Rodrigo", isExplicit: true),
        <PERSON>(title: "traitor", artist: "<PERSON> Rodrigo", isExplicit: false),
        <PERSON>(title: "drivers license", artist: "<PERSON> Rodrigo", isExplicit: true),
        <PERSON>(title: "1 step forward, 3 steps back", artist: "<PERSON> Rodrigo", isExplicit: true),
        <PERSON>(title: "deja vu", artist: "<PERSON> Rodrigo", isExplicit: true)
    ]
    
    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple.opacity(0.8),
                    Color.black
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Header with back button
                HStack {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Album Cover
                        VStack(spacing: 16) {
                            RoundedRectangle(cornerRadius: 8)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [Color.purple.opacity(0.6), Color.purple.opacity(0.9)]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 250, height: 250)
                                .overlay(
                                    VStack {
                                        Circle()
                                            .fill(Color.white.opacity(0.3))
                                            .frame(width: 120, height: 120)
                                            .overlay(
                                                Image(systemName: "person.fill")
                                                    .font(.system(size: 50))
                                                    .foregroundColor(.white.opacity(0.8))
                                            )
                                        
                                        HStack {
                                            Spacer()
                                            VStack {
                                                Rectangle()
                                                    .fill(Color.black.opacity(0.7))
                                                    .frame(width: 40, height: 20)
                                                    .overlay(
                                                        Text("EXPLICIT")
                                                            .font(.system(size: 6, weight: .bold))
                                                            .foregroundColor(.white)
                                                    )
                                            }
                                            .padding(.trailing, 8)
                                            .padding(.bottom, 8)
                                        }
                                    }
                                )
                            
                            // Album Info
                            VStack(spacing: 8) {
                                Text("SOUR")
                                    .font(.largeTitle)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                
                                HStack(spacing: 8) {
                                    Image(systemName: "checkmark.seal.fill")
                                        .foregroundColor(.blue)
                                        .font(.caption)
                                    
                                    Text("Olivia Rodrigo")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                }
                                
                                Text("Album • 2021")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.top, 20)
                        
                        // Control Buttons
                        HStack(spacing: 20) {
                            Button(action: {}) {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 50, height: 50)
                                    .overlay(
                                        Image(systemName: "plus")
                                            .foregroundColor(.white)
                                            .font(.title2)
                                    )
                            }
                            
                            Button(action: {}) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 50, height: 50)
                                    .overlay(
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.white)
                                            .font(.title2)
                                    )
                            }
                            
                            Button(action: {}) {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 50, height: 50)
                                    .overlay(
                                        Image(systemName: "arrow.down.circle")
                                            .foregroundColor(.white)
                                            .font(.title2)
                                    )
                            }
                            
                            Button(action: {}) {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 50, height: 50)
                                    .overlay(
                                        Image(systemName: "ellipsis")
                                            .foregroundColor(.white)
                                            .font(.title2)
                                    )
                            }
                            
                            Spacer()
                            
                            Button(action: {}) {
                                Image(systemName: "shuffle")
                                    .foregroundColor(.white)
                                    .font(.title)
                            }
                            
                            Button(action: {
                                isPlaying.toggle()
                            }) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 60, height: 60)
                                    .overlay(
                                        Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                                            .foregroundColor(.black)
                                            .font(.title2)
                                    )
                            }
                        }
                        .padding(.horizontal, 20)
                        
                        // Song List
                        VStack(spacing: 0) {
                            ForEach(Array(songs.enumerated()), id: \.offset) { index, song in
                                SongRowView(song: song, isCurrentlyPlaying: index == 1)
                            }
                        }
                        .padding(.top, 20)
                        
                        Spacer(minLength: 100)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .overlay(
            VStack {
                Spacer()
                
                // Mini Player
                MiniPlayerView()
                
                // Bottom Tab Bar
                BottomTabBar(selectedTab: $selectedBottomTab)
            }
        )
    }
}

struct Song {
    let title: String
    let artist: String
    let isExplicit: Bool
}

struct SongRowView: View {
    let song: Song
    let isCurrentlyPlaying: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 8) {
                    if isCurrentlyPlaying {
                        Image(systemName: "speaker.wave.2.fill")
                            .foregroundColor(.green)
                            .font(.caption)
                    }
                    
                    Text(song.title)
                        .font(.body)
                        .foregroundColor(isCurrentlyPlaying ? .green : .white)
                    
                    if song.isExplicit {
                        Rectangle()
                            .fill(Color.gray)
                            .frame(width: 12, height: 12)
                            .overlay(
                                Text("E")
                                    .font(.system(size: 8, weight: .bold))
                                    .foregroundColor(.black)
                            )
                    }
                }
                
                Text(song.artist)
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            Button(action: {}) {
                Image(systemName: "ellipsis")
                    .foregroundColor(.gray)
                    .font(.body)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
    }
}

struct MiniPlayerView: View {
    var body: some View {
        HStack(spacing: 12) {
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.purple.opacity(0.6))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: "person.fill")
                        .foregroundColor(.white)
                        .font(.caption)
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text("traitor")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text("Olivia Rodrigo")
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            Button(action: {}) {
                Image(systemName: "airplayaudio")
                    .foregroundColor(.white)
                    .font(.body)
            }
            
            Button(action: {}) {
                Image(systemName: "pause.fill")
                    .foregroundColor(.white)
                    .font(.title2)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            Color.black.opacity(0.8)
                .blur(radius: 10)
        )
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(.gray.opacity(0.3)),
            alignment: .top
        )
    }
}

#Preview {
    AlbumDetailView()
}

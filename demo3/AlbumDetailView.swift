//
//  AlbumDetailView.swift
//  demo3
//
//  Created by ltt on 2025/9/1.
//

import SwiftUI

struct AlbumDetailView: View {
    @State private var isPlaying = false
    @State private var selectedBottomTab = 0
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss
    
    let songs = [
        Song(title: "brutal", artist: "<PERSON> Rodrigo", isExplicit: true),
        <PERSON>(title: "traitor", artist: "<PERSON> Rodrigo", isExplicit: false),
        <PERSON>(title: "drivers license", artist: "<PERSON> Rodrigo", isExplicit: false),
        <PERSON>(title: "1 step forward, 3 steps back", artist: "<PERSON>", isExplicit: false),
        <PERSON>(title: "deja vu", artist: "<PERSON>", isExplicit: false),
        <PERSON>(title: "good 4 u", artist: "<PERSON> Rodrigo", isExplicit: true),
        <PERSON>(title: "enough for you", artist: "<PERSON> Rodrigo", isExplicit: false),
        <PERSON>(title: "happier", artist: "<PERSON>", isExplicit: false),
        <PERSON>(title: "jealousy, jealousy", artist: "<PERSON>", isExplicit: false),
        <PERSON>(title: "favorite crime", artist: "<PERSON> Rodrigo", isExplicit: false),
        <PERSON>(title: "hope ur ok", artist: "<PERSON> Rodrigo", isExplicit: false)
    ]
    
    var body: some View {
        ZStack {
            // Background gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple.opacity(0.8),
                    Color.black
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Header with back button
                HStack {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Album Cover
                        VStack(spacing: 16) {
                            RoundedRectangle(cornerRadius: 8)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [Color.purple.opacity(0.6), Color.purple.opacity(0.9)]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 200, height: 200)
                                .overlay(
                                    VStack {
                                        Circle()
                                            .fill(Color.white.opacity(0.3))
                                            .frame(width: 80, height: 80)
                                            .overlay(
                                                Image(systemName: "person.fill")
                                                    .font(.system(size: 30))
                                                    .foregroundColor(.white.opacity(0.8))
                                            )
                                        
                                        HStack {
                                            Spacer()
                                            VStack {
                                                Rectangle()
                                                    .fill(Color.black.opacity(0.7))
                                                    .frame(width: 50, height: 16)
                                                    .overlay(
                                                        Text("EXPLICIT")
                                                            .font(.system(size: 8, weight: .bold))
                                                            .foregroundColor(.white)
                                                    )
                                            }
                                            .padding(.trailing, 8)
                                            .padding(.bottom, 8)
                                        }
                                    }
                                )
                            
                            // Album Info
                            VStack(spacing: 6) {
                                Text("SOUR")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)

                                HStack(spacing: 6) {
                                    Image(systemName: "checkmark.seal.fill")
                                        .foregroundColor(.blue)
                                        .font(.caption)

                                    Text("Olivia Rodrigo")
                                        .font(.subheadline)
                                        .foregroundColor(.white)
                                }

                                Text("Album • 2021")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.top, 10)

                        // Control Buttons
                        HStack(spacing: 15) {
                            Button(action: {}) {
                                Circle()
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 44, height: 44)
                                    .overlay(
                                        Image(systemName: "plus")
                                            .foregroundColor(.white)
                                            .font(.title3)
                                    )
                            }
                            
                            Button(action: {}) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 44, height: 44)
                                    .overlay(
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.white)
                                            .font(.title3)
                                    )
                            }

                            Button(action: {}) {
                                Circle()
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 44, height: 44)
                                    .overlay(
                                        Image(systemName: "clock")
                                            .foregroundColor(.white)
                                            .font(.title3)
                                    )
                            }

                            Button(action: {}) {
                                Circle()
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 44, height: 44)
                                    .overlay(
                                        Image(systemName: "ellipsis")
                                            .foregroundColor(.white)
                                            .font(.title3)
                                    )
                            }
                            
                            Spacer()
                            
                            Button(action: {}) {
                                Circle()
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 44, height: 44)
                                    .overlay(
                                        Image(systemName: "shuffle")
                                            .foregroundColor(.white)
                                            .font(.title3)
                                    )
                            }

                            Button(action: {
                                isPlaying.toggle()
                            }) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 56, height: 56)
                                    .overlay(
                                        Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                                            .foregroundColor(.black)
                                            .font(.title2)
                                    )
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 15)

                        // Song List
                        LazyVStack(spacing: 0) {
                            ForEach(Array(songs.enumerated()), id: \.offset) { index, song in
                                SongRowView(song: song, isCurrentlyPlaying: index == 1)

                                if index < songs.count - 1 {
                                    Divider()
                                        .background(Color.white.opacity(0.1))
                                        .padding(.horizontal, 20)
                                }
                            }
                        }

                        Spacer(minLength: 120)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .overlay(
            VStack {
                Spacer()

                // Mini Player
                MiniPlayerView()

                // Bottom Tab Bar
                BottomTabBar(selectedTab: $selectedBottomTab)
            }
            .ignoresSafeArea(.keyboard, edges: .bottom)
        )
    }
}

struct Song {
    let title: String
    let artist: String
    let isExplicit: Bool
}

struct SongRowView: View {
    let song: Song
    let isCurrentlyPlaying: Bool

    var body: some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 3) {
                HStack(spacing: 6) {
                    Text(song.title)
                        .font(.body)
                        .foregroundColor(isCurrentlyPlaying ? .green : .white)
                        .lineLimit(1)

                    if song.isExplicit {
                        Rectangle()
                            .fill(Color.gray.opacity(0.6))
                            .frame(width: 14, height: 14)
                            .overlay(
                                Text("E")
                                    .font(.system(size: 9, weight: .bold))
                                    .foregroundColor(.white)
                            )
                    }

                    Spacer()
                }

                Text(song.artist)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(1)
            }

            Button(action: {}) {
                Image(systemName: "ellipsis")
                    .foregroundColor(.gray)
                    .font(.body)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }
}

struct MiniPlayerView: View {
    var body: some View {
        HStack(spacing: 12) {
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.purple.opacity(0.6))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: "person.fill")
                        .foregroundColor(.white)
                        .font(.caption)
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text("traitor")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text("Olivia Rodrigo")
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            Button(action: {}) {
                Image(systemName: "airplayaudio")
                    .foregroundColor(.white)
                    .font(.body)
            }
            
            Button(action: {}) {
                Image(systemName: "pause.fill")
                    .foregroundColor(.white)
                    .font(.title2)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            Color.black.opacity(0.8)
                .blur(radius: 10)
        )
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(.gray.opacity(0.3)),
            alignment: .top
        )
    }
}

// BottomTabBar组件 - 专辑详情页版本
struct BottomTabBar: View {
    @Binding var selectedTab: Int
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        HStack {
            TabBarItem(icon: "house.fill", title: "Home", isSelected: selectedTab == 0) {
                selectedTab = 0
                presentationMode.wrappedValue.dismiss()
            }

            Spacer()

            TabBarItem(icon: "magnifyingglass", title: "Search", isSelected: selectedTab == 1) {
                selectedTab = 1
                presentationMode.wrappedValue.dismiss()
            }

            Spacer()

            TabBarItem(icon: "books.vertical.fill", title: "Your Library", isSelected: selectedTab == 2) {
                selectedTab = 2
                presentationMode.wrappedValue.dismiss()
            }

            Spacer()

            TabBarItem(icon: "plus", title: "Create", isSelected: selectedTab == 3) {
                selectedTab = 3
                presentationMode.wrappedValue.dismiss()
            }
        }
        .padding(.horizontal, 30)
        .padding(.top, 12)
        .padding(.bottom, 34) // 为底部安全区域留出空间
        .background(
            Color.black.opacity(0.9)
                .ignoresSafeArea(.all, edges: .bottom)
        )
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(.gray.opacity(0.3)),
            alignment: .top
        )
    }
}

struct TabBarItem: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? .white : .gray)

                Text(title)
                    .font(.caption2)
                    .foregroundColor(isSelected ? .white : .gray)
            }
        }
    }
}

#Preview {
    AlbumDetailView()
}

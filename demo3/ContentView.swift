//
//  ContentView.swift
//  demo3
//
//  Created by ltt on 2025/9/1.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedTab = "All"
    @State private var selectedBottomTab = 0

    let tabs = ["All", "Music", "Podcasts", "Audiobooks"]

    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()

                VStack(spacing: 0) {
                    // Top Tab Bar
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(tabs, id: \.self) { tab in
                                Button(action: {
                                    selectedTab = tab
                                }) {
                                    Text(tab)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(selectedTab == tab ? .black : .white)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            RoundedRectangle(cornerRadius: 20)
                                                .fill(selectedTab == tab ? Color.green : Color.gray.opacity(0.3))
                                        )
                                }
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                    .padding(.top, 10)

                    // Main Content
                    ScrollView {
                        VStack(alignment: .leading, spacing: 20) {
                            // Music Grid
                            LazyVGrid(columns: [
                                GridItem(.flexible()),
                                GridItem(.flexible())
                            ], spacing: 12) {
                                MusicCard(title: "Brat and it's completely diff...",
                                         subtitle: "",
                                         imageName: "music1",
                                         backgroundColor: .green)

                                MusicCard(title: "Wicked Official Playlist",
                                         subtitle: "",
                                         imageName: "music2",
                                         backgroundColor: .orange)

                                MusicCard(title: "Gracie Abrams",
                                         subtitle: "",
                                         imageName: "artist1",
                                         backgroundColor: .clear)

                                MusicCard(title: "More Life",
                                         subtitle: "",
                                         imageName: "music3",
                                         backgroundColor: .clear)

                                MusicCard(title: "DJ",
                                         subtitle: "",
                                         imageName: "dj",
                                         backgroundColor: .blue)

                                MusicCard(title: "Today's Top Hits",
                                         subtitle: "",
                                         imageName: "tophits",
                                         backgroundColor: .clear)

                                MusicCard(title: "eternal sunshine",
                                         subtitle: "",
                                         imageName: "eternal",
                                         backgroundColor: .clear)

                                MusicCard(title: "Short n' Sweet",
                                         subtitle: "",
                                         imageName: "shortsweet",
                                         backgroundColor: .clear)
                            }
                            .padding(.horizontal, 20)

                            // Picked for you section
                            VStack(alignment: .leading, spacing: 16) {
                                Text("Picked for you")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)

                                PodcastCard()
                                    .padding(.horizontal, 20)
                            }

                            // New releases section
                            VStack(alignment: .leading, spacing: 16) {
                                Text("New releases for you")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)

                                ScrollView(.horizontal, showsIndicators: false) {
                                    HStack(spacing: 12) {
                                        AlbumCard(imageName: "album1")
                                        AlbumCard(imageName: "album2")
                                        AlbumCard(imageName: "album3")
                                    }
                                    .padding(.horizontal, 20)
                                }
                            }
                        }
                        .padding(.top, 20)
                    }

                    Spacer()
                }
            }
        }
        .overlay(
            // Bottom Tab Bar
            VStack {
                Spacer()
                BottomTabBar(selectedTab: $selectedBottomTab)
            }
        )
    }
}

// MARK: - Supporting Views

struct MusicCard: View {
    let title: String
    let subtitle: String
    let imageName: String
    let backgroundColor: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(backgroundColor == .clear ? Color.gray.opacity(0.3) : backgroundColor)
                    .frame(height: 80)

                if imageName.contains("artist") {
                    Circle()
                        .fill(Color.gray.opacity(0.6))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: "person.fill")
                                .foregroundColor(.white)
                        )
                } else if imageName.contains("dj") {
                    Circle()
                        .fill(Color.blue)
                        .frame(width: 50, height: 50)
                        .overlay(
                            Text("DJ")
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                } else {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.6))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: "music.note")
                                .foregroundColor(.white)
                        )
                }
            }

            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .lineLimit(2)
                .padding(.top, 8)
        }
    }
}

struct PodcastCard: View {
    var body: some View {
        HStack(spacing: 12) {
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.pink.opacity(0.8))
                .frame(width: 120, height: 120)
                .overlay(
                    VStack {
                        Circle()
                            .fill(Color.white)
                            .frame(width: 60, height: 60)
                            .overlay(
                                Image(systemName: "mouth.fill")
                                    .foregroundColor(.pink)
                                    .font(.title2)
                            )
                        Text("Sounds\nLike A Cult")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                    }
                )

            VStack(alignment: .leading, spacing: 4) {
                Text("Podcast")
                    .font(.caption)
                    .foregroundColor(.gray)

                Text("Sounds Like A Cult")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Text("A podcast about the modern-day \"cults\" we all follow.")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(3)

                Spacer()
            }

            Spacer()

            Button(action: {}) {
                Image(systemName: "ellipsis")
                    .foregroundColor(.gray)
                    .font(.title2)
            }
        }
        .frame(height: 120)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.2))
        )
    }
}

struct AlbumCard: View {
    let imageName: String

    var body: some View {
        VStack {
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.6))
                .frame(width: 120, height: 120)
                .overlay(
                    Image(systemName: "music.note")
                        .foregroundColor(.white)
                        .font(.title)
                )
        }
    }
}

struct BottomTabBar: View {
    @Binding var selectedTab: Int

    var body: some View {
        HStack {
            TabBarItem(icon: "house.fill", title: "Home", isSelected: selectedTab == 0) {
                selectedTab = 0
            }

            Spacer()

            TabBarItem(icon: "magnifyingglass", title: "Search", isSelected: selectedTab == 1) {
                selectedTab = 1
            }

            Spacer()

            TabBarItem(icon: "books.vertical.fill", title: "Your Library", isSelected: selectedTab == 2) {
                selectedTab = 2
            }

            Spacer()

            TabBarItem(icon: "plus", title: "Create", isSelected: selectedTab == 3) {
                selectedTab = 3
            }
        }
        .padding(.horizontal, 30)
        .padding(.vertical, 12)
        .background(
            Color.black.opacity(0.9)
                .blur(radius: 10)
        )
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(.gray.opacity(0.3)),
            alignment: .top
        )
    }
}

struct TabBarItem: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? .white : .gray)

                Text(title)
                    .font(.caption2)
                    .foregroundColor(isSelected ? .white : .gray)
            }
        }
    }
}

#Preview {
    ContentView()
}
